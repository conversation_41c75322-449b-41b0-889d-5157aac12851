import React, { useEffect, useRef, useContext } from 'react';
import {
    StyleSheet,
    View,
    Text,
    TouchableOpacity,
    Animated,
    Easing,
    Dimensions,
    StatusBar,
    SafeAreaView,
} from 'react-native';
import { useRouter } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { ThemeContext } from '../context/ThemeContext';

const { width, height } = Dimensions.get('window');

export default function Index() {
    const router = useRouter();

    // Try to get theme context, fallback to default values if not available
    let theme, isDarkMode;
    try {
        const themeContext = useContext(ThemeContext);
        theme = themeContext?.theme || {
            PRIMARY: '#2A8E9E',
            SECONDARY: '#001d3d',
            WHITE: '#FFFFFF',
            SHADOW: '#000000',
        };
        isDarkMode = themeContext?.isDarkMode || false;
    } catch {
        // Fallback theme if context is not available
        theme = {
            PRIMARY: '#2A8E9E',
            SECONDARY: '#001d3d',
            WHITE: '#FFFFFF',
            SHADOW: '#000000',
        };
        isDarkMode = false;
    }

    // Animation refs
    const fadeAnim = useRef(new Animated.Value(0)).current;
    const slideAnim = useRef(new Animated.Value(50)).current;
    const buttonScale = useRef(new Animated.Value(1)).current;

    useEffect(() => {
        Animated.parallel([
            Animated.timing(fadeAnim, {
                toValue: 1,
                duration: 800,
                useNativeDriver: true,
            }),
            Animated.timing(slideAnim, {
                toValue: 0,
                duration: 800,
                easing: Easing.out(Easing.exp),
                useNativeDriver: true,
            }),
        ]).start();
    }, [fadeAnim, slideAnim]);

    const handlePressIn = () => {
        Animated.spring(buttonScale, {
            toValue: 0.95,
            friction: 8,
            useNativeDriver: true,
        }).start();
    };

    const handlePressOut = () => {
        Animated.spring(buttonScale, {
            toValue: 1,
            friction: 8,
            useNativeDriver: true,
        }).start();
    };

    return (
        <SafeAreaView
            style={[styles.container, { backgroundColor: theme.PRIMARY }]}
        >
            <StatusBar
                barStyle={isDarkMode ? 'light-content' : 'dark-content'}
                backgroundColor={theme.PRIMARY}
            />

            {/* Simplified Content */}
            <Animated.View
                style={[
                    styles.contentContainer,
                    {
                        opacity: fadeAnim,
                        transform: [{ translateY: slideAnim }],
                    },
                ]}
            >
                <Text style={[styles.title, { color: theme.WHITE }]}>
                    Welcome to BUILD-CONNECT
                </Text>
                <Text style={[styles.subtitle, { color: theme.WHITE }]}>
                    Your Land & Construction Hub
                </Text>

                {/* Background Gradient */}
                <LinearGradient
                    colors={[theme.PRIMARY, theme.SECONDARY]}
                    style={styles.background}
                    start={{ x: 0.5, y: 0.6 }}
                    end={{ x: 1.9, y: 0 }}
                >
                    {/* Simple Button */}
                    <View style={styles.buttonContainer}>
                        <TouchableOpacity
                            onPress={() => router.push('/auth/Login')}
                            onPressIn={handlePressIn}
                            onPressOut={handlePressOut}
                            activeOpacity={0.9}
                        >
                            <Animated.View
                                style={[
                                    styles.button,
                                    { shadowColor: theme.SHADOW },
                                    { transform: [{ scale: buttonScale }] },
                                ]}
                            >
                                <LinearGradient
                                    colors={['#ffffff', '#f8f8f8']}
                                    style={styles.buttonGradient}
                                >
                                    <Text
                                        style={[
                                            styles.buttonText,
                                            { color: theme.PRIMARY },
                                        ]}
                                    >
                                        Get Started
                                    </Text>
                                    <Ionicons
                                        name="arrow-forward"
                                        size={22}
                                        color={theme.PRIMARY}
                                        style={styles.buttonIcon}
                                    />
                                </LinearGradient>
                            </Animated.View>
                        </TouchableOpacity>
                    </View>
                </LinearGradient>
            </Animated.View>
        </SafeAreaView>
    );
}

const styles = StyleSheet.create({
    container: {
        flex: 1,
    },
    contentContainer: {
        flex: 1,
        paddingHorizontal: 24,
        alignItems: 'center',
        justifyContent: 'center',
        zIndex: 3,
    },
    background: {
        position: 'absolute',
        left: 0,
        right: 0,
        top: height * 0.2,
        bottom: 0,
        zIndex: 2,
        borderTopRightRadius: 20,
        borderTopLeftRadius: 20,
    },
    title: {
        fontSize: width * 0.08,
        fontWeight: 'bold',
        textAlign: 'center',
        marginBottom: 10,
    },
    subtitle: {
        fontSize: width * 0.04,
        textAlign: 'center',
        marginBottom: 40,
    },
    buttonContainer: {
        width: '100%',
        marginTop: height * 0.06,
        alignItems: 'center',
    },
    button: {
        width: width * 0.8,
        borderRadius: 16,
        overflow: 'hidden',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.2,
        shadowRadius: 5,
        elevation: 5,
    },
    buttonGradient: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center',
        paddingVertical: 16,
    },
    buttonText: {
        fontSize: 18,
        fontWeight: 'bold',
    },
    buttonIcon: {
        marginLeft: 8,
    },
});
