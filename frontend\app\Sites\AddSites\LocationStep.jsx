import React from 'react';
import {
    View,
    Text,
    TextInput,
    TouchableOpacity,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import { styles } from '../siteStyles';
import MapSelector from '../MapSelector';

const LocationStep = ({
    theme,
    fields,
    setFields,
    region,
    setRegion,
    marker,
    setMarker,
    hasPermission,
    setHasPermission,
    setStep,
}) => {
    const handleNext = () => {
        setStep('encumbrance');
    };

    const handleBack = () => {
        setStep('siteDetails');
    };

    return (
        <>
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Location Details
            </Text>

            {/* Location */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="location-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Location"
                    value={fields.location}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, location: text }))
                    }
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Latitude */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="navigate-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Latitude"
                    value={fields.latitude}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, latitude: text }))
                    }
                    keyboardType="numeric"
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Longitude */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="navigate-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Longitude"
                    value={fields.longitude}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, longitude: text }))
                    }
                    keyboardType="numeric"
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Map Selector */}
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Select Location on Map
            </Text>
            <View style={styles.mapContainer}>
                <MapSelector
                    region={region}
                    setRegion={setRegion}
                    marker={marker}
                    setMarker={setMarker}
                    hasPermission={hasPermission}
                    setHasPermission={setHasPermission}
                    fields={fields}
                    setFields={setFields}
                    theme={theme}
                />
            </View>

            {/* Navigation Buttons */}
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={[
                        styles.backButton,
                        { backgroundColor: theme.GRAY_LIGHT },
                    ]}
                    onPress={handleBack}
                    activeOpacity={0.8}
                >
                    <Text
                        style={[
                            styles.backButtonText,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Back
                    </Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={[
                        styles.nextButton,
                        { shadowColor: theme.PRIMARY },
                    ]}
                    onPress={handleNext}
                    activeOpacity={0.8}
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.nextButtonGradient}
                    >
                        <Text
                            style={[
                                styles.nextButtonText,
                                { color: theme.WHITE },
                            ]}
                        >
                            Next
                        </Text>
                    </LinearGradient>
                </TouchableOpacity>
            </View>
        </>
    );
};

export default LocationStep;
