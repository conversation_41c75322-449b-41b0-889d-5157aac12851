import React from 'react';
import { View, Text, TouchableOpacity, ActivityIndicator } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { styles } from '../siteStyles';

const ReviewStep = ({ theme, formik, setStep, isSubmitting }) => {
    const { values, handleSubmit } = formik;
    const handleBack = () => {
        setStep('propertyTax');
    };

    return (
        <>
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Review Your Site Details
            </Text>

            <Text style={[styles.reviewText, { color: theme.TEXT_PRIMARY }]}>
                Please review all the information below before submitting your
                site listing.
            </Text>

            {/* Basic Details Section */}
            <View
                style={[
                    styles.reviewSection,
                    {
                        backgroundColor: theme.ACCENT,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Text
                    style={[
                        styles.reviewSectionTitle,
                        { color: theme.PRIMARY },
                    ]}
                >
                    Basic Details
                </Text>
                <Text
                    style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}
                >
                    Site Name: {values.name || 'Not provided'}
                </Text>
                <Text
                    style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}
                >
                    Address: {values.addressLine1 || 'Not provided'}
                    {values.addressLine2 ? `, ${values.addressLine2}` : ''}
                </Text>
                <Text
                    style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}
                >
                    Landmark: {values.landmark || 'Not provided'}
                </Text>
                <Text
                    style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}
                >
                    Pincode: {values.pincode || 'Not provided'}
                </Text>
                <Text
                    style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}
                >
                    State: {values.state || 'Not provided'}
                </Text>
                <Text
                    style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}
                >
                    District: {values.district || 'Not provided'}
                </Text>
                <Text
                    style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}
                >
                    Plot Area: {values.plotArea || 'Not provided'} sq ft
                </Text>
                <Text
                    style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}
                >
                    Price: ₹{values.price || 'Not provided'}
                </Text>
            </View>

            {/* Location Details Section */}
            <View
                style={[
                    styles.reviewSection,
                    {
                        backgroundColor: theme.ACCENT,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Text
                    style={[
                        styles.reviewSectionTitle,
                        { color: theme.PRIMARY },
                    ]}
                >
                    Location Details
                </Text>
                <Text
                    style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}
                >
                    Location: {values.location || 'Not provided'}
                </Text>
                <Text
                    style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}
                >
                    Latitude: {values.latitude || 'Not provided'}
                </Text>
                <Text
                    style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}
                >
                    Longitude: {values.longitude || 'Not provided'}
                </Text>
            </View>

            {/* Encumbrance Details Section */}
            <View
                style={[
                    styles.reviewSection,
                    {
                        backgroundColor: theme.ACCENT,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Text
                    style={[
                        styles.reviewSectionTitle,
                        { color: theme.PRIMARY },
                    ]}
                >
                    Encumbrance Certificate
                </Text>
                <Text
                    style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}
                >
                    Document Number:{' '}
                    {values.encumbranceDocNumber || 'Not provided'}
                </Text>
                <Text
                    style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}
                >
                    Date:{' '}
                    {values.encumbranceDate
                        ? values.encumbranceDate.toLocaleDateString()
                        : 'Not provided'}
                </Text>
                <Text
                    style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}
                >
                    Village: {values.village || 'Not provided'}
                </Text>
                <Text
                    style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}
                >
                    Survey Number: {values.surveyNumber || 'Not provided'}
                </Text>
                <Text
                    style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}
                >
                    Certificate:{' '}
                    {values.encumbranceCert ? 'Uploaded' : 'Not uploaded'}
                </Text>
            </View>

            {/* Property Tax Details Section */}
            <View
                style={[
                    styles.reviewSection,
                    {
                        backgroundColor: theme.ACCENT,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Text
                    style={[
                        styles.reviewSectionTitle,
                        { color: theme.PRIMARY },
                    ]}
                >
                    Property Tax Receipt
                </Text>
                <Text
                    style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}
                >
                    Tax Number: {values.propertyTaxNumber || 'Not provided'}
                </Text>
                <Text
                    style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}
                >
                    Date:{' '}
                    {values.propertyTaxDate
                        ? values.propertyTaxDate.toLocaleDateString()
                        : 'Not provided'}
                </Text>
                <Text
                    style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}
                >
                    Receipt:{' '}
                    {values.propertyTaxRec ? 'Uploaded' : 'Not uploaded'}
                </Text>
            </View>

            {/* Documents Section */}
            <View
                style={[
                    styles.reviewSection,
                    {
                        backgroundColor: theme.ACCENT,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Text
                    style={[
                        styles.reviewSectionTitle,
                        { color: theme.PRIMARY },
                    ]}
                >
                    Documents
                </Text>
                <Text
                    style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}
                >
                    Site Images: {values.siteImages?.length || 0} uploaded
                </Text>
            </View>

            {/* Navigation Buttons */}
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={[
                        styles.backButton,
                        { backgroundColor: theme.GRAY_LIGHT },
                    ]}
                    onPress={handleBack}
                    activeOpacity={0.8}
                    disabled={isSubmitting}
                >
                    <Text
                        style={[
                            styles.backButtonText,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Back
                    </Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={[
                        styles.submitButton,
                        { shadowColor: theme.PRIMARY },
                        isSubmitting && styles.submitButtonDisabled,
                    ]}
                    onPress={handleSubmit}
                    activeOpacity={0.8}
                    disabled={isSubmitting}
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.submitButtonGradient}
                    >
                        {isSubmitting ? (
                            <View style={styles.loadingContainer}>
                                <ActivityIndicator
                                    size="small"
                                    color={theme.WHITE}
                                />
                                <Text
                                    style={[
                                        styles.submitButtonText,
                                        { color: theme.WHITE, marginLeft: 8 },
                                    ]}
                                >
                                    Submitting...
                                </Text>
                            </View>
                        ) : (
                            <Text
                                style={[
                                    styles.submitButtonText,
                                    { color: theme.WHITE },
                                ]}
                            >
                                Submit Site
                            </Text>
                        )}
                    </LinearGradient>
                </TouchableOpacity>
            </View>
        </>
    );
};

export default ReviewStep;
