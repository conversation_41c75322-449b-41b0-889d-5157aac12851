import React from 'react';
import {
    View,
    Text,
    TouchableOpacity,
    ActivityIndicator,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { styles } from '../siteStyles';

const ReviewStep = ({ theme, fields, setStep, handleSubmit, isSubmitting }) => {
    const handleBack = () => {
        setStep('propertyTax');
    };

    return (
        <>
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Review Your Site Details
            </Text>

            <Text style={[styles.reviewText, { color: theme.TEXT_PRIMARY }]}>
                Please review all the information below before submitting your site listing.
            </Text>

            {/* Basic Details Section */}
            <View
                style={[
                    styles.reviewSection,
                    {
                        backgroundColor: theme.ACCENT,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Text style={[styles.reviewSectionTitle, { color: theme.PRIMARY }]}>
                    Basic Details
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    Site Name: {fields.name || 'Not provided'}
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    Address: {fields.addressLine1 || 'Not provided'}
                    {fields.addressLine2 ? `, ${fields.addressLine2}` : ''}
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    Landmark: {fields.landmark || 'Not provided'}
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    Pincode: {fields.pincode || 'Not provided'}
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    State: {fields.state || 'Not provided'}
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    District: {fields.district || 'Not provided'}
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    Plot Area: {fields.plotArea || 'Not provided'} sq ft
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    Price: ₹{fields.price || 'Not provided'}
                </Text>
            </View>

            {/* Location Details Section */}
            <View
                style={[
                    styles.reviewSection,
                    {
                        backgroundColor: theme.ACCENT,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Text style={[styles.reviewSectionTitle, { color: theme.PRIMARY }]}>
                    Location Details
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    Location: {fields.location || 'Not provided'}
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    Latitude: {fields.latitude || 'Not provided'}
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    Longitude: {fields.longitude || 'Not provided'}
                </Text>
            </View>

            {/* Encumbrance Details Section */}
            <View
                style={[
                    styles.reviewSection,
                    {
                        backgroundColor: theme.ACCENT,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Text style={[styles.reviewSectionTitle, { color: theme.PRIMARY }]}>
                    Encumbrance Certificate
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    Owner Name: {fields.encOwnerName || 'Not provided'}
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    Document Number: {fields.encDocumentNo || 'Not provided'}
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    Survey Number: {fields.surveyNo || 'Not provided'}
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    Village: {fields.village || 'Not provided'}
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    Sub District: {fields.subDistrict || 'Not provided'}
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    District: {fields.District || 'Not provided'}
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    Certificate: {fields.encumbranceCert ? 'Uploaded' : 'Not uploaded'}
                </Text>
            </View>

            {/* Property Tax Details Section */}
            <View
                style={[
                    styles.reviewSection,
                    {
                        backgroundColor: theme.ACCENT,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Text style={[styles.reviewSectionTitle, { color: theme.PRIMARY }]}>
                    Property Tax Receipt
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    Owner Name: {fields.ptrOwnerName || 'Not provided'}
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    Receipt Number: {fields.ptrReciptNo || 'Not provided'}
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    Receipt: {fields.propertyTaxRec ? 'Uploaded' : 'Not uploaded'}
                </Text>
            </View>

            {/* Documents Section */}
            <View
                style={[
                    styles.reviewSection,
                    {
                        backgroundColor: theme.ACCENT,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Text style={[styles.reviewSectionTitle, { color: theme.PRIMARY }]}>
                    Documents
                </Text>
                <Text style={[styles.reviewItem, { color: theme.TEXT_PRIMARY }]}>
                    Site Images: {fields.siteImages?.length || 0} uploaded
                </Text>
            </View>

            {/* Navigation Buttons */}
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={[
                        styles.backButton,
                        { backgroundColor: theme.GRAY_LIGHT },
                    ]}
                    onPress={handleBack}
                    activeOpacity={0.8}
                    disabled={isSubmitting}
                >
                    <Text
                        style={[
                            styles.backButtonText,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Back
                    </Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={[
                        styles.submitButton,
                        { shadowColor: theme.PRIMARY },
                        isSubmitting && styles.submitButtonDisabled,
                    ]}
                    onPress={handleSubmit}
                    activeOpacity={0.8}
                    disabled={isSubmitting}
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.submitButtonGradient}
                    >
                        {isSubmitting ? (
                            <View style={styles.loadingContainer}>
                                <ActivityIndicator
                                    size="small"
                                    color={theme.WHITE}
                                />
                                <Text
                                    style={[
                                        styles.submitButtonText,
                                        { color: theme.WHITE, marginLeft: 8 },
                                    ]}
                                >
                                    Submitting...
                                </Text>
                            </View>
                        ) : (
                            <Text
                                style={[
                                    styles.submitButtonText,
                                    { color: theme.WHITE },
                                ]}
                            >
                                Submit Site
                            </Text>
                        )}
                    </LinearGradient>
                </TouchableOpacity>
            </View>
        </>
    );
};

export default ReviewStep;
