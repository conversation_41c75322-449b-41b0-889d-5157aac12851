import React, { useState } from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
import { styles } from '../siteStyles';
import DocumentPreviewModal from '../../Components/Profile/DocumentPreviewModal';

const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'application/pdf'];
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5 MB

const EncumbranceStep = ({
    theme,
    values,
    setFieldValue,
    errors,
    touched,
    setStep,
    validateForm,
}) => {
    const [previewModal, setPreviewModal] = useState({
        visible: false,
        url: '',
        type: '',
    });

    const handleNext = async () => {
        const stepErrors = await validateForm();
        const hasStepErrors = Object.keys(stepErrors).some((key) =>
            [
                'encOwnerName',
                'encDocumentNo',
                'surveyNo',
                'village',
                'subDistrict',
                'District',
                'encumbranceCert',
            ].includes(key)
        );

        if (!hasStepErrors) {
            setStep('propertyTax');
        }
    };

    const handleBack = () => {
        setStep('location');
    };

    const previewDocument = (document, type) => {
        if (document && document.uri) {
            setPreviewModal({
                visible: true,
                url: document.uri,
                type: type,
            });
        }
    };

    const pickEncumbranceDocument = async () => {
        try {
            const res = await DocumentPicker.getDocumentAsync({
                type: ALLOWED_TYPES,
            });
            if (res.canceled) return;

            const asset = res.assets[0];
            if (asset.size > MAX_FILE_SIZE) {
                Alert.alert('File too large', 'Max 5 MB allowed.');
                return;
            }
            if (!ALLOWED_TYPES.includes(asset.mimeType)) {
                Alert.alert('Invalid type', 'Choose JPG, PNG, or PDF');
                return;
            }

            setFieldValue('encumbranceCert', asset);
        } catch (error) {
            Alert.alert('Error', 'Failed to select file');
        }
    };

    return (
        <>
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Encumbrance Certificate Details
            </Text>

            {/* Owner Name */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="person-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Owner name*"
                    value={values.encOwnerName}
                    onChangeText={(text) => setFieldValue('encOwnerName', text)}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Document Number */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="document-text-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Document number*"
                    value={values.encDocumentNo}
                    onChangeText={(text) =>
                        setFieldValue('encDocumentNo', text)
                    }
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Survey Number */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="map-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Survey number"
                    value={values.surveyNo}
                    onChangeText={(text) => setFieldValue('surveyNo', text)}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Village */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="home-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Village"
                    value={fields.village}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, village: text }))
                    }
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Sub District */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="location-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Sub district"
                    value={fields.subDistrict}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, subDistrict: text }))
                    }
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* District */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="map-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="District"
                    value={fields.District}
                    onChangeText={(text) =>
                        setFields((prev) => ({ ...prev, District: text }))
                    }
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>

            {/* Encumbrance Certificate Upload */}
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Upload Encumbrance Certificate
            </Text>
            <TouchableOpacity
                style={[
                    styles.filePickerButton,
                    {
                        backgroundColor: fields.encumbranceCert
                            ? theme.ACCENT
                            : theme.INPUT_BACKGROUND,
                        borderColor: fields.encumbranceCert
                            ? theme.PRIMARY
                            : theme.INPUT_BORDER,
                    },
                ]}
                onPress={pickEncumbranceDocument}
            >
                <Ionicons
                    name={
                        fields.encumbranceCert
                            ? 'checkmark-circle'
                            : 'document-attach-outline'
                    }
                    size={22}
                    color={
                        fields.encumbranceCert
                            ? theme.PRIMARY
                            : theme.TEXT_SECONDARY
                    }
                    style={styles.inputIcon}
                />
                <Text
                    style={[
                        styles.filePickerText,
                        {
                            color: fields.encumbranceCert
                                ? theme.PRIMARY
                                : theme.TEXT_SECONDARY,
                        },
                    ]}
                >
                    {fields.encumbranceCert
                        ? `✓ ${fields.encumbranceCert.name}`
                        : 'Encumbrance Certificate (JPG/PNG/PDF)*'}
                </Text>
            </TouchableOpacity>

            {/* Preview Button */}
            {fields.encumbranceCert && (
                <TouchableOpacity
                    style={[
                        styles.previewButton,
                        {
                            backgroundColor: theme.ACCENT,
                            borderColor: theme.PRIMARY,
                        },
                    ]}
                    onPress={() =>
                        previewDocument(
                            fields.encumbranceCert,
                            'Encumbrance Certificate'
                        )
                    }
                    activeOpacity={0.8}
                >
                    <Ionicons
                        name="eye-outline"
                        size={20}
                        color={theme.PRIMARY}
                        style={styles.inputIcon}
                    />
                    <Text
                        style={[
                            styles.previewButtonText,
                            { color: theme.PRIMARY },
                        ]}
                    >
                        Preview Document
                    </Text>
                </TouchableOpacity>
            )}

            {/* Navigation Buttons */}
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={[
                        styles.backButton,
                        { backgroundColor: theme.GRAY_LIGHT },
                    ]}
                    onPress={handleBack}
                    activeOpacity={0.8}
                >
                    <Text
                        style={[
                            styles.backButtonText,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Back
                    </Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={[styles.nextButton, { shadowColor: theme.PRIMARY }]}
                    onPress={handleNext}
                    activeOpacity={0.8}
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.nextButtonGradient}
                    >
                        <Text
                            style={[
                                styles.nextButtonText,
                                { color: theme.WHITE },
                            ]}
                        >
                            Next
                        </Text>
                    </LinearGradient>
                </TouchableOpacity>
            </View>

            <DocumentPreviewModal
                visible={previewModal.visible}
                documentUrl={previewModal.url}
                documentType={previewModal.type}
                onClose={() =>
                    setPreviewModal({ visible: false, url: '', type: '' })
                }
            />
        </>
    );
};

export default EncumbranceStep;
