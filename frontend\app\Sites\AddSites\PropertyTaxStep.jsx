import React from 'react';
import { View, Text, TextInput, TouchableOpacity, Alert } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Ionicons } from '@expo/vector-icons';
import * as DocumentPicker from 'expo-document-picker';
import { styles } from '../siteStyles';
import {
    FILE_CONSTRAINTS,
    validateStepData,
} from '../validations/siteValidations';

const PropertyTaxStep = ({
    theme,
    formik,
    setStep,
    isSubmitting,
    onDocumentPreview,
}) => {
    const { values, errors, touched, setFieldValue, handleChange, handleBlur } =
        formik;

    const handleNext = async () => {
        const stepValidation = await validateStepData('propertyTax', values);
        if (stepValidation.isValid) {
            setStep('review');
        } else {
            // Show first error
            const firstError = Object.values(stepValidation.errors)[0];
            if (firstError) {
                Alert.alert('Validation Error', firstError);
            }
        }
    };

    const handleBack = () => {
        setStep('encumbrance');
    };

    const previewDocument = (document, type) => {
        if (document && document.uri && onDocumentPreview) {
            onDocumentPreview(document.uri, type);
        }
    };

    const pickPropertyTaxDocument = async () => {
        try {
            const res = await DocumentPicker.getDocumentAsync({
                type: FILE_CONSTRAINTS.ALLOWED_TYPES,
                multiple: false,
            });
            if (res.canceled) return;

            const asset = res.assets[0];
            if (asset.size > FILE_CONSTRAINTS.MAX_FILE_SIZE) {
                Alert.alert('File too large', 'Max 5 MB allowed.');
                return;
            }
            if (!FILE_CONSTRAINTS.ALLOWED_TYPES.includes(asset.mimeType)) {
                Alert.alert('Invalid type', 'Choose JPG, PNG, or PDF');
                return;
            }

            setFieldValue('propertyTaxRec', asset);
        } catch (error) {
            Alert.alert('Error', 'Failed to select file');
        }
    };

    return (
        <>
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Property Tax Receipt Details
            </Text>

            {/* Property Tax Number */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="receipt-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Property tax number*"
                    value={values.propertyTaxNumber}
                    onChangeText={handleChange('propertyTaxNumber')}
                    onBlur={handleBlur('propertyTaxNumber')}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>
            {errors.propertyTaxNumber && touched.propertyTaxNumber && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.propertyTaxNumber}
                </Text>
            )}

            {/* Property Tax Date */}
            <View
                style={[
                    styles.inputContainer,
                    {
                        backgroundColor: theme.INPUT_BACKGROUND,
                        borderColor: theme.INPUT_BORDER,
                    },
                ]}
            >
                <Ionicons
                    name="calendar-outline"
                    size={22}
                    color={theme.PRIMARY}
                    style={styles.inputIcon}
                />
                <TextInput
                    placeholder="Property tax date*"
                    value={
                        values.propertyTaxDate
                            ? values.propertyTaxDate.toLocaleDateString()
                            : ''
                    }
                    editable={false}
                    style={[styles.input, { color: theme.TEXT_PRIMARY }]}
                    placeholderTextColor={theme.TEXT_SECONDARY}
                />
            </View>
            {errors.propertyTaxDate && touched.propertyTaxDate && (
                <Text style={[styles.errorText, { color: theme.ERROR }]}>
                    {errors.propertyTaxDate}
                </Text>
            )}

            {/* Property Tax Receipt Upload */}
            <Text style={[styles.sectionTitle, { color: theme.PRIMARY }]}>
                Upload Property Tax Receipt
            </Text>
            <TouchableOpacity
                style={[
                    styles.filePickerButton,
                    {
                        backgroundColor: values.propertyTaxRec
                            ? theme.ACCENT
                            : theme.INPUT_BACKGROUND,
                        borderColor: values.propertyTaxRec
                            ? theme.PRIMARY
                            : theme.INPUT_BORDER,
                    },
                ]}
                onPress={pickPropertyTaxDocument}
            >
                <Ionicons
                    name={
                        values.propertyTaxRec
                            ? 'checkmark-circle'
                            : 'receipt-outline'
                    }
                    size={22}
                    color={
                        values.propertyTaxRec
                            ? theme.PRIMARY
                            : theme.TEXT_SECONDARY
                    }
                    style={styles.inputIcon}
                />
                <Text
                    style={[
                        styles.filePickerText,
                        {
                            color: values.propertyTaxRec
                                ? theme.PRIMARY
                                : theme.TEXT_SECONDARY,
                        },
                    ]}
                >
                    {values.propertyTaxRec
                        ? `✓ ${values.propertyTaxRec.name}`
                        : 'Property Tax Receipt (JPG/PNG/PDF)*'}
                </Text>
            </TouchableOpacity>

            {/* Preview Button */}
            {values.propertyTaxRec && (
                <TouchableOpacity
                    style={[
                        styles.previewButton,
                        {
                            backgroundColor: theme.ACCENT,
                            borderColor: theme.PRIMARY,
                        },
                    ]}
                    onPress={() =>
                        previewDocument(
                            values.propertyTaxRec,
                            'Property Tax Receipt'
                        )
                    }
                    activeOpacity={0.8}
                >
                    <Ionicons
                        name="eye-outline"
                        size={20}
                        color={theme.PRIMARY}
                        style={styles.inputIcon}
                    />
                    <Text
                        style={[
                            styles.previewButtonText,
                            { color: theme.PRIMARY },
                        ]}
                    >
                        Preview Document
                    </Text>
                </TouchableOpacity>
            )}

            {/* Navigation Buttons */}
            <View style={styles.buttonContainer}>
                <TouchableOpacity
                    style={[
                        styles.backButton,
                        { backgroundColor: theme.GRAY_LIGHT },
                    ]}
                    onPress={handleBack}
                    activeOpacity={0.8}
                >
                    <Text
                        style={[
                            styles.backButtonText,
                            { color: theme.TEXT_PRIMARY },
                        ]}
                    >
                        Back
                    </Text>
                </TouchableOpacity>
                <TouchableOpacity
                    style={[styles.nextButton, { shadowColor: theme.PRIMARY }]}
                    onPress={handleNext}
                    activeOpacity={0.8}
                >
                    <LinearGradient
                        colors={[theme.PRIMARY, theme.SECONDARY]}
                        start={{ x: 0, y: 0 }}
                        end={{ x: 1, y: 0 }}
                        style={styles.nextButtonGradient}
                    >
                        <Text
                            style={[
                                styles.nextButtonText,
                                { color: theme.WHITE },
                            ]}
                        >
                            Review
                        </Text>
                    </LinearGradient>
                </TouchableOpacity>
            </View>
        </>
    );
};

export default PropertyTaxStep;
